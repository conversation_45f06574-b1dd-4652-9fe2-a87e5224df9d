<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'

const data = ref([
  {
    code: 200,
    message: '修改商品价格成功',
    createAt: '2025-01-01 00:00:00',
  },
  {
    code: 200,
    message: '修改本地库存成功',
    createAt: '2025-01-01 00:00:00',
  },
  {
    code: 200,
    message: '修改本地库存成功',
    createAt: '2025-01-01 00:00:00',
  },
  {
    code: 200,
    message: '修改本地库存成功',
    createAt: '2025-01-01 00:00:00',
  },
  {
    code: 200,
    message: '修改本地库存成功',
    createAt: '2025-01-01 00:00:00',
  },
  {
    code: 200,
    message: '修改本地库存成功',
    createAt: '2025-01-01 00:00:00',
  },
  {
    code: 200,
    message: '修改本地库存成功',
    createAt: '2025-01-01 00:00:00',
  },
  {
    code: 200,
    message: '修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功修改本地库存成功',
    createAt: '2025-01-01 00:00:00',
  }
])

const tableRef = ref<any>(null);

// 滚动到底部的函数
const scrollToBottom = async () => {
  await nextTick();
  const tableBody = tableRef.value?.$el?.querySelector('.arco-table-body');
  if (tableBody) {
    tableBody.scrollTop = tableBody.scrollHeight;
  }
}

// 监听数据变化，自动滚动到底部
watch(data, scrollToBottom, { deep: true, flush: 'post' })
</script>

<template>
  <a-table ref="tableRef" class="h-164px" row-key="createAt" :bordered="{ cell: true }" :data="data" size="small" :pagination="false" sticky-header>
    <template #columns>
      <a-table-column title="操作状态" :width="64" align="center">
        <template #cell="{ record }">
          <a-tag v-if="record.code === 200" size="small" color="green">成功</a-tag>
          <a-tag v-else size="small" color="red">失败</a-tag>
        </template>
      </a-table-column>
      <a-table-column title="消息内容" :width="280" align="left" data-index="message" ellipsis tooltip></a-table-column>
      <a-table-column title="操作时间" :width="100" align="center" data-index="createAt"></a-table-column>
    </template>
  </a-table>
</template>

<style scoped lang="less"></style>